# AuraESIM WebApp PRD

## 1. 产品概述

AuraESIM 是一款面向全球旅行者的 eSIM 管理应用，允许用户浏览、购买、管理和充值 eSIM 套餐。应用采用 WebApp 形式，但提供接近原生应用的用户体验。

### 1.1 目标用户

- 国际旅行者
- 经常出差的商务人士
- 需要在多个国家/地区使用移动数据的用户
- 寻找灵活数据套餐的用户

### 1.2 核心价值

- 简化 eSIM 购买和管理流程
- 提供全球范围内的数据套餐选择
- 透明展示套餐详情和使用情况
- 便捷的充值和续费功能

## 2. 技术架构

### 2.1 前端技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS 4
- **UI 组件**: shadcn/ui
- **包管理器**: Bun
- **状态管理**: Redux Toolkit
- **数据请求**: native fetch
- **支付库**: Airwallex
- **生产部署**: Docker
- **其他库**:
  - Embla Carousel (轮播组件)
  - Lucide React (图标)
  - React Hook Form (表单处理)
  - Zod (表单验证)
  - Sonner (通知提示)

### 2.2 设计原则

- 移动优先设计
- 响应式布局
- 原生应用般的交互体验
- 简洁直观的用户界面
- 流畅的动画和过渡效果

### 2.3 核心技术特性

- **PWA 支持**: 实现渐进式 Web 应用，支持安装到主屏幕和离线访问
- **国际化**: 支持多语言（首批支持英文、中文、日文）
- **暗黑模式**: 自动跟随系统主题切换
- **简化登录**: 使用邮件验证码登录，无账号自动注册

## 3. 功能规格

应用分为三个主要部分，通过底部导航栏访问：

### 3.1 首页

#### 3.1.1 顶部区域
- 应用名称 "AuraESIM" 显示在左侧
- 搜索图标位于右侧

#### 3.1.2 搜索功能
- 点击搜索图标展开全屏搜索界面
- 搜索条件：
  - 国家和地区
  - 区域
- 搜索结果实时显示
- 支持历史搜索记录

#### 3.1.3 首页轮播图
- 自动轮播展示促销活动和特色套餐
- 支持手动滑动切换
- 点击可跳转到相应详情页

#### 3.1.4 推荐地区
- 横向可滑动的卡片布局
- 每个卡片包含：
  - 地区名称
  - 代表性图标或国旗
  - 简短描述或价格信息
- 包括热门地区如：美国、欧洲、亚洲、中国、日本等

#### 3.1.5 套餐列表
- 分类展示方式：
  - 按区域：全球、中国（大陆+港澳）、亚洲（11个地区）等
  - 按大洲→国家/地区
- 热门套餐标记 "HOT" 标签
- 每个套餐卡片显示：
  - 国家/地区名称和国旗
  - 数据流量
  - 有效期
  - 价格
  - 简短描述

### 3.2 eSIMs 页面

#### 3.2.1 页面标题
- "我的 eSIMs" 显示在顶部

#### 3.2.2 状态标签页
- 两个标签页：有效、已过期
- 默认显示"有效"标签页

#### 3.2.3 eSIM 卡片
- 卡片显示内容：
  - 国家/地区名称和国旗
  - 剩余流量（带进度条可视化）
  - 剩余有效期（天数和小时数）
  - 套餐名称

#### 3.2.4 eSIM 详情页
点击卡片进入详情页，包含：
- 详细使用情况
  - 剩余流量：xxGB/xxxGB
  - 剩余时间：xx天xx小时/xxx天
- 订单明细
  - 订单号
  - 订单时间
  - 激活时间
  - 更新时间
  - 支付金额
- 套餐说明（文字描述）
- 覆盖和网络信息
  - 覆盖区域
  - 支持的运营商
  - 网络类型（4G/5G等）
- 充值按钮（如果支持充值）

### 3.3 我的页面

#### 3.3.1 个人资料
- 用户头像
- 用户名
- 手机号/邮箱
- "编辑资料"按钮（跳转到编辑页面）

#### 3.3.2 账户余额
- 卡片式展示当前余额
- "充值"按钮
- "查看详情"链接（跳转到流水明细页面）
  - 交易记录列表
  - 筛选和搜索功能

#### 3.3.3 推荐码
- 显示用户专属推荐码
- 复制功能
- 分享功能（生成分享图或链接）
- 推荐奖励说明

#### 3.3.4 常见问题
- 手风琴式展开/折叠问题和答案
- 按类别组织问题
- 搜索功能

#### 3.3.5 联系我们
- 客服联系方式
- 邮箱
- 社交媒体链接
- 在线客服入口

#### 3.3.6 关于
- 应用版本信息
- 公司简介
- 隐私政策链接
- 用户协议链接

#### 3.3.7 设置
- 语言选择（英文、中文、日文）
- 主题设置（跟随系统、浅色、深色）
- 通知设置

## 4. 用户流程

### 4.1 首次使用流程
1. 启动应用
2. 展示欢迎页/引导页
3. 选择语言
4. 登录（邮箱验证码，无账号自动注册）
5. 完成后进入首页

### 4.2 购买 eSIM 流程
1. 在首页浏览或搜索套餐
2. 选择套餐查看详情
3. 点击购买按钮
4. 确认订单信息
5. 选择支付方式
6. 完成支付
7. 显示激活指南

### 4.3 管理 eSIM 流程
1. 进入"eSIMs"页面
2. 查看已购买的 eSIM 列表
3. 选择特定 eSIM 查看详情
4. 可选操作：充值、查看使用情况

### 4.4 充值流程
1. 在 eSIM 详情页点击"充值"
2. 选择充值套餐
3. 确认订单
4. 完成支付
5. 显示充值成功页面

## 5. UI/UX 设计规范

### 5.1 色彩系统
- 主色调：品牌蓝色
- 辅助色：白色、浅灰色
- 强调色：橙色（用于标记热门、促销等）
- 状态色：
  - 成功：绿色
  - 警告：黄色
  - 错误：红色
  - 信息：蓝色
- 暗黑模式对应色板

### 5.2 排版
- 使用 Geist 字体系统（已在项目中配置）
- 清晰的层级结构
- 适当的字体大小和行高

### 5.3 交互设计
- 触摸友好的大尺寸点击目标（最小 44px）
- 滑动手势支持
- 下拉刷新
- 平滑过渡动画
- 即时反馈

### 5.4 加载状态
- 骨架屏加载效果
- 进度指示器
- 加载动画

## 6. 技术实现注意事项

### 6.1 性能优化
- 图片懒加载
- 组件代码分割
- 缓存策略
- 预加载关键资源

### 6.2 PWA 实现
- Web App Manifest 配置
- Service Worker 实现
- 离线功能支持
- 安装到主屏幕提示
- 推送通知支持

### 6.3 国际化实现
- 使用 i18n 框架管理翻译
- 支持动态切换语言
- 考虑文本长度变化对布局的影响
- 日期、货币等本地化处理

### 6.4 安全考虑
- 安全的用户认证
- 数据加密
- 防止 XSS 和 CSRF 攻击
- 敏感信息保护

### 6.5 可访问性
- 符合 WCAG 2.1 标准
- 屏幕阅读器支持
- 键盘导航
- 足够的颜色对比度

## 7. 未来功能规划

### 7.1 第一阶段扩展
- 更多语言支持
- 用量分析图表
- 更多支付方式

### 7.2 第二阶段扩展
- 家庭共享计划
- 自动续费选项
- 忠诚度积分系统
- 更多区域覆盖

## 8. 附录

### 8.1 术语表
- **eSIM**: 嵌入式 SIM 卡，无需物理 SIM 卡即可连接移动网络
- **流量包**: 预付费数据套餐
- **覆盖区域**: eSIM 可使用的地理区域
- **激活**: 开始使用 eSIM 的过程

### 8.2 参考资料
- 移动用户体验设计最佳实践
- eSIM 技术规范
- 支付流程安全标准
- PWA 开发指南
