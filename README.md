# AuraESIM WebApp

A modern, responsive web application for managing eSIM plans for international travelers. Built with Next.js 15, TypeScript, and shadcn/ui components.

## 🌟 Features

### Core Functionality
- **Browse eSIM Plans**: Discover data plans for 100+ countries and regions
- **Search & Filter**: Find plans by country, region, or specific requirements
- **Plan Management**: View active and expired eSIM plans with usage tracking
- **Purchase Flow**: Secure checkout process with multiple payment options
- **User Profile**: Manage account settings, balance, and referral codes

### User Experience
- **Mobile-First Design**: Optimized for mobile devices with responsive layout
- **PWA Support**: Install as a native app with offline capabilities
- **Dark/Light Theme**: Automatic theme switching based on system preferences
- **Multi-language**: Support for English, Chinese, and Japanese (ready for i18n)
- **Real-time Updates**: Live data usage tracking and notifications

### Technical Features
- **Modern Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Component Library**: shadcn/ui for consistent, accessible UI components
- **State Management**: Ready for Redux Toolkit integration
- **Form Handling**: React Hook Form with Zod validation
- **Notifications**: Toast notifications with <PERSON><PERSON>
- **Icons**: Lucide React icon library

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ or Bun
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd esim-front/webapp
```

2. Install dependencies:
```bash
bun install
```

3. Start the development server:
```bash
bun dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📱 Pages & Features

### Home Page (`/`)
- Hero carousel with promotional content
- Popular destinations showcase
- Featured eSIM plans
- Global search functionality

### eSIMs Management (`/esims`)
- Active eSIM plans with usage tracking
- Expired plans history
- Detailed plan information
- Top-up and renewal options

### Profile (`/profile`)
- User account information
- Account balance and transaction history
- Referral code sharing
- Quick access to settings and support

### Additional Pages
- **Login** (`/login`): Email-based authentication
- **Plan Details** (`/plans/[id]`): Detailed plan information and reviews
- **Checkout** (`/checkout`): Secure payment processing
- **Success** (`/checkout/success`): Purchase confirmation and activation
- **Settings** (`/settings`): App preferences and configuration
- **FAQ** (`/faq`): Frequently asked questions
- **Contact** (`/contact`): Customer support
- **About** (`/about`): App information and legal links

## 🛠 Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **UI Components**: shadcn/ui
- **Package Manager**: Bun
- **Icons**: Lucide React
- **Forms**: React Hook Form + Zod
- **Notifications**: Sonner
- **Theme**: next-themes
- **Carousel**: Embla Carousel

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (auth)/            # Authentication pages
│   ├── esims/             # eSIM management
│   ├── profile/           # User profile
│   ├── plans/             # Plan details
│   ├── checkout/          # Purchase flow
│   └── ...
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── bottom-nav.tsx    # Bottom navigation
│   ├── main-layout.tsx   # Main layout wrapper
│   └── ...
├── types/                # TypeScript type definitions
├── lib/                  # Utility functions
└── hooks/               # Custom React hooks
```

## 🎨 Design System

The app uses a consistent design system based on:
- **Colors**: Primary brand colors with dark/light mode support
- **Typography**: Geist font family for optimal readability
- **Spacing**: Consistent spacing scale using Tailwind CSS
- **Components**: shadcn/ui for accessible, customizable components
- **Icons**: Lucide React for consistent iconography

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file for environment-specific configuration:
```env
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_STRIPE_KEY=your_stripe_key
```

### PWA Configuration
The app includes PWA support with:
- Web App Manifest (`/public/manifest.json`)
- Service Worker (ready for implementation)
- App icons and splash screens

## 🚀 Deployment

### Build for Production
```bash
bun run build
```

### Start Production Server
```bash
bun start
```

### Docker Deployment
```bash
docker build -t auraesim-webapp .
docker run -p 3000:3000 auraesim-webapp
```

## 🔮 Future Enhancements

### Phase 1
- Backend API integration
- Real payment processing
- User authentication system
- Push notifications

### Phase 2
- Advanced analytics dashboard
- Loyalty program
- Auto-renewal features
- Enhanced offline support

## 📄 License

This project is proprietary software. All rights reserved.

## 🤝 Contributing

This is a private project. For internal development guidelines, please refer to the team documentation.

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Internal Slack: #auraesim-dev
