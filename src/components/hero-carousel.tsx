"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface CarouselItem {
  id: string;
  title: string;
  description: string;
  image: string;
  badge?: string;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline";
  link: string;
}

const mockCarouselItems: CarouselItem[] = [
  {
    id: "1",
    title: "Global eSIM Plans",
    description: "Stay connected worldwide with our premium data plans",
    image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=200&fit=crop",
    badge: "Popular",
    badgeVariant: "default",
    link: "/plans/global",
  },
  {
    id: "2",
    title: "Europe Travel Special",
    description: "30% off on all European eSIM plans this month",
    image: "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=400&h=200&fit=crop",
    badge: "30% OFF",
    badgeVariant: "destructive",
    link: "/plans/europe",
  },
  {
    id: "3",
    title: "Asia Pacific Plans",
    description: "High-speed data across 15+ Asian countries",
    image: "https://images.unsplash.com/photo-1480796927426-f609979314bd?w=400&h=200&fit=crop",
    badge: "New",
    badgeVariant: "secondary",
    link: "/plans/asia",
  },
];

export function HeroCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % mockCarouselItems.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToPrevious = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? mockCarouselItems.length - 1 : prev - 1
    );
    setIsAutoPlaying(false);
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % mockCarouselItems.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <div className="relative w-full">
      <div className="overflow-hidden rounded-lg">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {mockCarouselItems.map((item) => (
            <div key={item.id} className="w-full flex-shrink-0">
              <Card className="border-0 shadow-none">
                <CardContent className="p-0">
                  <div className="relative aspect-[2/1] overflow-hidden rounded-lg">
                    <div 
                      className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"
                      style={{
                        backgroundImage: `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${item.image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                      }}
                    />
                    <div className="absolute inset-0 flex flex-col justify-center p-6 text-white">
                      {item.badge && (
                        <Badge 
                          variant={item.badgeVariant} 
                          className="w-fit mb-2"
                        >
                          {item.badge}
                        </Badge>
                      )}
                      <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                      <p className="text-sm opacity-90 mb-4">{item.description}</p>
                      <Button 
                        variant="secondary" 
                        size="sm" 
                        className="w-fit"
                        onClick={() => {
                          // Handle navigation to item.link
                          console.log('Navigate to:', item.link);
                        }}
                      >
                        Learn More
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <Button
        variant="outline"
        size="icon"
        className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 bg-white/80 hover:bg-white"
        onClick={goToPrevious}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 bg-white/80 hover:bg-white"
        onClick={goToNext}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>

      {/* Dots Indicator */}
      <div className="flex justify-center gap-2 mt-4">
        {mockCarouselItems.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full transition-colors ${
              index === currentIndex ? 'bg-primary' : 'bg-muted-foreground/30'
            }`}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>
    </div>
  );
}
