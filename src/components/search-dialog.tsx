"use client";

import { useState } from "react";
import { Search, X, Clock, MapPin } from "lucide-react";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface SearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Mock data for demonstration
const mockSearchResults = [
  { id: "1", type: "country", name: "United States", flag: "🇺🇸", planCount: 12 },
  { id: "2", type: "country", name: "Japan", flag: "🇯🇵", planCount: 8 },
  { id: "3", type: "country", name: "United Kingdom", flag: "🇬🇧", planCount: 6 },
  { id: "4", type: "region", name: "Europe", flag: "🇪🇺", planCount: 25 },
  { id: "5", type: "region", name: "Asia", flag: "🌏", planCount: 18 },
];

const mockRecentSearches = [
  "United States",
  "Europe",
  "Japan",
];

export function SearchDialog({ open, onOpenChange }: SearchDialogProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState(mockSearchResults);

  // Redirect to search page when dialog opens
  if (open) {
    onOpenChange(false);
    window.location.href = '/search';
    return null;
  }

  const handleSearch = (value: string) => {
    setQuery(value);
    if (value.trim()) {
      // Filter mock results based on query
      const filtered = mockSearchResults.filter(item =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setResults(filtered);
    } else {
      setResults(mockSearchResults);
    }
  };

  const clearSearch = () => {
    setQuery("");
    setResults(mockSearchResults);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-full max-h-screen w-full max-w-md p-0 gap-0">
        <DialogHeader className="p-4 pb-0">
          <div className="flex items-center gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search countries or regions..."
                value={query}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 pr-10"
                autoFocus
              />
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 p-0"
                  onClick={clearSearch}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1 px-4">
          {!query && (
            <div className="space-y-4 py-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Recent Searches
                </h3>
                <div className="space-y-2">
                  {mockRecentSearches.map((search, index) => (
                    <button
                      key={index}
                      className="flex items-center gap-3 w-full p-2 text-left hover:bg-muted rounded-lg transition-colors"
                      onClick={() => handleSearch(search)}
                    >
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{search}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {query && (
            <div className="space-y-4 py-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Search Results
                </h3>
                <div className="space-y-2">
                  {results.length > 0 ? (
                    results.map((result) => (
                      <button
                        key={result.id}
                        className="flex items-center justify-between w-full p-3 text-left hover:bg-muted rounded-lg transition-colors"
                        onClick={() => {
                          // Handle result selection
                          onOpenChange(false);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{result.flag}</span>
                          <div>
                            <div className="font-medium">{result.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {result.planCount} plans available
                            </div>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {result.type}
                        </Badge>
                      </button>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No results found for "{query}"</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
