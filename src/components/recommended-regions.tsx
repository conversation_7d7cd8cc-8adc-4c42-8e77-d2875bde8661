"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

interface Region {
  id: string;
  name: string;
  flag: string;
  description: string;
  startingPrice: number;
  currency: string;
  planCount: number;
  isPopular?: boolean;
}

const mockRegions: Region[] = [
  {
    id: "1",
    name: "United States",
    flag: "🇺🇸",
    description: "Unlimited high-speed data",
    startingPrice: 9.99,
    currency: "USD",
    planCount: 12,
    isPopular: true,
  },
  {
    id: "2",
    name: "Europe",
    flag: "🇪🇺",
    description: "30+ countries coverage",
    startingPrice: 15.99,
    currency: "USD",
    planCount: 25,
    isPopular: true,
  },
  {
    id: "3",
    name: "Japan",
    flag: "🇯🇵",
    description: "5G network available",
    startingPrice: 12.99,
    currency: "USD",
    planCount: 8,
  },
  {
    id: "4",
    name: "China",
    flag: "🇨🇳",
    description: "Mainland + Hong Kong",
    startingPrice: 8.99,
    currency: "USD",
    planCount: 6,
  },
  {
    id: "5",
    name: "Asia Pacific",
    flag: "🌏",
    description: "15+ countries",
    startingPrice: 19.99,
    currency: "USD",
    planCount: 18,
  },
  {
    id: "6",
    name: "Global",
    flag: "🌍",
    description: "Worldwide coverage",
    startingPrice: 29.99,
    currency: "USD",
    planCount: 50,
    isPopular: true,
  },
];

export function RecommendedRegions() {
  const handleRegionClick = (region: Region) => {
    // Handle navigation to region plans
    console.log('Navigate to region:', region.id);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Popular Destinations</h2>
        <button className="text-sm text-primary hover:underline">
          View All
        </button>
      </div>
      
      <ScrollArea className="w-full whitespace-nowrap">
        <div className="flex gap-4 pb-4">
          {mockRegions.map((region) => (
            <Card
              key={region.id}
              className="flex-shrink-0 w-40 cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleRegionClick(region)}
            >
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl">{region.flag}</span>
                    {region.isPopular && (
                      <Badge variant="secondary" className="text-xs">
                        HOT
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    <h3 className="font-medium text-sm leading-tight">
                      {region.name}
                    </h3>
                    <p className="text-xs text-muted-foreground leading-tight">
                      {region.description}
                    </p>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="text-sm font-semibold">
                      From ${region.startingPrice}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {region.planCount} plans
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
