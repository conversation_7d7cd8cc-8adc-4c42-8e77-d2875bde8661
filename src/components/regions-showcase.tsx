"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

interface RegionCard {
  id: string;
  name: string;
  flag: string;
  startingPrice: number;
  currency: string;
  description?: string;
  gradient: string;
  isPopular?: boolean;
}

interface CountryCard {
  id: string;
  name: string;
  flag: string;
  isHot?: boolean;
}

const featuredRegions: RegionCard[] = [
  {
    id: "us",
    name: "美国",
    flag: "🇺🇸",
    startingPrice: 0.70,
    currency: "US$",
    gradient: "from-blue-500 to-blue-600",
    isPopular: true,
  },
  {
    id: "europe",
    name: "欧洲",
    flag: "🌍",
    startingPrice: 0.52,
    currency: "US$",
    description: "(37个国家)",
    gradient: "from-purple-500 to-purple-600",
    isPopular: true,
  },
  {
    id: "asia",
    name: "亚洲",
    flag: "🌏",
    startingPrice: 0.70,
    currency: "US$",
    description: "(11个地区)",
    gradient: "from-pink-500 to-pink-600",
    isPopular: true,
  },
];

const regionTabs = [
  "多地区", "北美洲", "欧洲", "亚洲", "非洲", "大洋洲", "南美洲"
];

const globalRegions = [
  { id: "esim-trial", name: "eSIM 试用", flag: "🌍", isHot: false },
  { id: "global-130", name: "全球 (130+地区)", flag: "🌍", isHot: true },
  { id: "global-140", name: "全球 (140+地区)", flag: "🌍", isHot: true },
  { id: "china-mainland", name: "中国 (大陆+港澳)", flag: "🇨🇳", isHot: false },
  { id: "south-america-15", name: "南美洲 (15+个国家/地区)", flag: "🌎", isHot: false },
  { id: "middle-east-10", name: "中东 (10+国家)", flag: "🌍", isHot: false },
  { id: "south-america-30", name: "南美 (30+国家)", flag: "🌎", isHot: false },
  { id: "europe-plus", name: "欧洲 Plus", flag: "🇪🇺", isHot: true },
  { id: "europe-37", name: "欧洲 (37个国家)", flag: "🇪🇺", isHot: true },
  { id: "asia-11", name: "亚洲 (11个地区)", flag: "🌏", isHot: true },
  { id: "north-america", name: "美国和加拿大", flag: "🌎", isHot: false },
  { id: "oceania", name: "新西兰和澳大利亚", flag: "🇦🇺", isHot: false },
];

const northAmericaCountries = [
  { id: "us", name: "美国", flag: "🇺🇸", isHot: true },
  { id: "ca", name: "加拿大", flag: "🇨🇦", isHot: true },
  { id: "mx", name: "墨西哥", flag: "🇲🇽", isHot: false },
  { id: "no", name: "多米尼加", flag: "🇩🇴", isHot: false },
  { id: "gt", name: "瓜德罗普", flag: "🇬🇵", isHot: false },
  { id: "gr", name: "马提尼克", flag: "🇲🇶", isHot: false },
  { id: "cu", name: "伯利兹", flag: "🇧🇿", isHot: false },
  { id: "bb", name: "百慕大", flag: "🇧🇲", isHot: false },
];

const europeCountries = [
  { id: "tr", name: "土耳其", flag: "🇹🇷", isHot: true },
  { id: "gb", name: "英国", flag: "🇬🇧", isHot: true },
  { id: "fr", name: "法国", flag: "🇫🇷", isHot: true },
  { id: "es", name: "西班牙", flag: "🇪🇸", isHot: true },
  { id: "it", name: "意大利", flag: "🇮🇹", isHot: true },
  { id: "ch", name: "瑞士", flag: "🇨🇭", isHot: true },
];

export function RegionsShowcase() {
  const [activeTab, setActiveTab] = useState("多地区");

  const getCurrentRegions = () => {
    switch (activeTab) {
      case "多地区":
        return globalRegions;
      case "北美洲":
        return northAmericaCountries;
      case "欧洲":
        return europeCountries;
      default:
        return globalRegions;
    }
  };

  const handleRegionClick = (region: RegionCard | CountryCard) => {
    // Navigate to country plans page
    if ('flag' in region && region.id.length === 2) {
      // It's a country
      window.location.href = `/plans/country/${region.id}`;
    } else {
      // It's a region
      console.log('Navigate to region:', region.id);
    }
  };

  return (
    <div className="space-y-6">
      {/* Featured Regions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">推荐地区</h2>
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex gap-4 pb-4">
            {featuredRegions.map((region) => (
              <Card
                key={region.id}
                className={`flex-shrink-0 w-36 cursor-pointer hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br ${region.gradient} text-white overflow-hidden`}
                onClick={() => handleRegionClick(region)}
              >
                <CardContent className="p-4 h-24 flex flex-col justify-between">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl">{region.flag}</span>
                    {region.isPopular && (
                      <Badge variant="secondary" className="text-xs bg-white/20 text-white border-0">
                        HOT
                      </Badge>
                    )}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{region.name}</div>
                    {region.description && (
                      <div className="text-xs opacity-80">{region.description}</div>
                    )}
                    <div className="text-xs font-medium mt-1">
                      {region.currency} {region.startingPrice.toFixed(2)} 起
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Region Tabs */}
      <div className="space-y-4">
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex gap-2 pb-2">
            {regionTabs.map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveTab(tab)}
                className="whitespace-nowrap rounded-full"
              >
                {tab}
              </Button>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        {/* Region Grid */}
        <div className="space-y-3">
          {activeTab !== "多地区" && (
            <div className="text-sm text-muted-foreground font-medium">
              {activeTab}
            </div>
          )}
          <div className="grid grid-cols-2 gap-3">
            {getCurrentRegions().map((region) => (
              <Card
                key={region.id}
                className="cursor-pointer hover:shadow-md transition-all duration-200 border border-border/50 bg-card/50 backdrop-blur-sm"
                onClick={() => handleRegionClick(region)}
              >
                <CardContent className="p-4 flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                    <span className="text-lg">{region.flag}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">{region.name}</div>
                  </div>
                  {region.isHot && (
                    <Badge variant="destructive" className="text-xs">
                      HOT
                    </Badge>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
