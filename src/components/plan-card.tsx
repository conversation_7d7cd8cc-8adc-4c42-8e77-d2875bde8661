"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Wifi, Clock, Globe } from "lucide-react";
import type { ESIMPlan } from "@/types";

interface PlanCardProps {
  plan: ESIMPlan;
  onSelect?: (plan: ESIMPlan) => void;
}

export function PlanCard({ plan, onSelect }: PlanCardProps) {
  const handleSelect = () => {
    onSelect?.(plan);
  };

  const formatDataAmount = (amount: number) => {
    if (amount >= 1000) {
      return `${amount / 1000}TB`;
    }
    return `${amount}GB`;
  };

  const formatValidityDays = (days: number) => {
    if (days >= 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays === 0) {
        return `${months} month${months > 1 ? 's' : ''}`;
      }
      return `${months}m ${remainingDays}d`;
    }
    return `${days} day${days > 1 ? 's' : ''}`;
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <span className="text-lg">
                {plan.countries.length === 1 ? 
                  getCountryFlag(plan.countries[0]) : 
                  plan.isGlobal ? '🌍' : '🌏'
                }
              </span>
              <div>
                <h3 className="font-medium text-sm">{plan.name}</h3>
                <p className="text-xs text-muted-foreground">
                  {plan.region}
                </p>
              </div>
            </div>
            {plan.isHot && (
              <Badge variant="destructive" className="text-xs">
                HOT
              </Badge>
            )}
          </div>

          {/* Plan Details */}
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="space-y-1">
              <Wifi className="h-4 w-4 mx-auto text-muted-foreground" />
              <div className="text-sm font-medium">
                {formatDataAmount(plan.dataAmount)}
              </div>
              <div className="text-xs text-muted-foreground">Data</div>
            </div>
            <div className="space-y-1">
              <Clock className="h-4 w-4 mx-auto text-muted-foreground" />
              <div className="text-sm font-medium">
                {formatValidityDays(plan.validityDays)}
              </div>
              <div className="text-xs text-muted-foreground">Validity</div>
            </div>
            <div className="space-y-1">
              <Globe className="h-4 w-4 mx-auto text-muted-foreground" />
              <div className="text-sm font-medium">
                {plan.countries.length}
              </div>
              <div className="text-xs text-muted-foreground">
                {plan.countries.length === 1 ? 'Country' : 'Countries'}
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-xs text-muted-foreground line-clamp-2">
            {plan.description}
          </p>

          {/* Network Types */}
          <div className="flex gap-1">
            {plan.networkTypes.map((network) => (
              <Badge key={network} variant="outline" className="text-xs">
                {network}
              </Badge>
            ))}
          </div>

          {/* Price and Action */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-bold">
                ${plan.price}
              </div>
              <div className="text-xs text-muted-foreground">
                {plan.currency}
              </div>
            </div>
            <Button size="sm" onClick={handleSelect}>
              Select Plan
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to get country flag (simplified)
function getCountryFlag(countryCode: string): string {
  const flagMap: Record<string, string> = {
    'US': '🇺🇸',
    'UK': '🇬🇧',
    'JP': '🇯🇵',
    'CN': '🇨🇳',
    'DE': '🇩🇪',
    'FR': '🇫🇷',
    'IT': '🇮🇹',
    'ES': '🇪🇸',
    'KR': '🇰🇷',
    'TH': '🇹🇭',
    'SG': '🇸🇬',
    'AU': '🇦🇺',
    'CA': '🇨🇦',
    'BR': '🇧🇷',
    'IN': '🇮🇳',
  };
  return flagMap[countryCode] || '🌍';
}
