"use client";

import { BottomNav } from "./bottom-nav";
import { cn } from "@/lib/utils";

interface MainLayoutProps {
  children: React.ReactNode;
  showBottomNav?: boolean;
}

export function MainLayout({ children, showBottomNav = true }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <main className={cn(
        "mx-auto max-w-md",
        showBottomNav && "pb-16"
      )}>
        {children}
      </main>
      {showBottomNav && <BottomNav />}
    </div>
  );
}
