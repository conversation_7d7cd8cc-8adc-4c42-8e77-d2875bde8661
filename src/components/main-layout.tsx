"use client";

import { BottomNav } from "./bottom-nav";
import { cn } from "@/lib/utils";

interface MainLayoutProps {
  children: React.ReactNode;
  showBottomNav?: boolean;
}

export function MainLayout({ children, showBottomNav = true }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50/50">
      <main className={cn(
        "mx-auto max-w-md bg-white min-h-screen",
        showBottomNav && "pb-20"
      )}>
        {children}
      </main>
      {showBottomNav && <BottomNav />}
    </div>
  );
}
