"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Globe, 
  Palette, 
  Bell, 
  Moon, 
  Sun, 
  Monitor
} from "lucide-react";

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
];

const themes = [
  { value: 'system', label: 'System', icon: Monitor },
  { value: 'light', label: 'Light', icon: Sun },
  { value: 'dark', label: 'Dark', icon: Moon },
];

export default function SettingsPage() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [language, setLanguage] = useState('en');
  const [notifications, setNotifications] = useState({
    dataUsage: true,
    expiration: true,
    promotions: false,
    news: false,
  });

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold">Settings</h1>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* Language Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Language
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <div className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Theme Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Appearance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {themes.map((themeOption) => {
                  const Icon = themeOption.icon;
                  return (
                    <button
                      key={themeOption.value}
                      className={`flex items-center gap-3 w-full p-3 rounded-lg border transition-colors ${
                        theme === themeOption.value
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:bg-muted/50'
                      }`}
                      onClick={() => setTheme(themeOption.value)}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{themeOption.label}</span>
                      {theme === themeOption.value && (
                        <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
                      )}
                    </button>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Data Usage Alerts</div>
                  <div className="text-sm text-muted-foreground">
                    Get notified when you're running low on data
                  </div>
                </div>
                <Switch
                  checked={notifications.dataUsage}
                  onCheckedChange={() => handleNotificationChange('dataUsage')}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Expiration Reminders</div>
                  <div className="text-sm text-muted-foreground">
                    Remind me before my eSIM expires
                  </div>
                </div>
                <Switch
                  checked={notifications.expiration}
                  onCheckedChange={() => handleNotificationChange('expiration')}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Promotions & Offers</div>
                  <div className="text-sm text-muted-foreground">
                    Get notified about special deals and discounts
                  </div>
                </div>
                <Switch
                  checked={notifications.promotions}
                  onCheckedChange={() => handleNotificationChange('promotions')}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">News & Updates</div>
                  <div className="text-sm text-muted-foreground">
                    Stay updated with new features and destinations
                  </div>
                </div>
                <Switch
                  checked={notifications.news}
                  onCheckedChange={() => handleNotificationChange('news')}
                />
              </div>
            </CardContent>
          </Card>

          {/* App Information */}
          <Card>
            <CardContent className="p-4">
              <div className="text-center space-y-2">
                <div className="text-sm text-muted-foreground">
                  AuraESIM Version 1.0.0
                </div>
                <div className="text-xs text-muted-foreground">
                  Built with ❤️ for travelers worldwide
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
