"use client";

import { useState } from "react";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Wallet, 
  Gift, 
  HelpCircle, 
  MessageCircle, 
  Info, 
  Settings, 
  ChevronRight,
  Copy,
  Share,
  Plus
} from "lucide-react";
import { toast } from "sonner";

// Mock user data
const mockUser = {
  id: "1",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "",
  balance: 45.67,
  referralCode: "AURA2024JD",
  createdAt: "2024-01-01T00:00:00Z",
};

const menuItems = [
  {
    icon: HelpCircle,
    title: "FAQ",
    description: "Frequently asked questions",
    href: "/faq",
  },
  {
    icon: MessageCircle,
    title: "Contact Us",
    description: "Get help and support",
    href: "/contact",
  },
  {
    icon: Info,
    title: "About",
    description: "App info and legal",
    href: "/about",
  },
  {
    icon: Settings,
    title: "Settings",
    description: "Language, theme, notifications",
    href: "/settings",
  },
];

export default function ProfilePage() {
  const [user] = useState(mockUser);

  const handleCopyReferralCode = () => {
    navigator.clipboard.writeText(user.referralCode);
    toast.success("Referral code copied to clipboard!");
  };

  const handleShareReferralCode = () => {
    if (navigator.share) {
      navigator.share({
        title: "Join AuraESIM",
        text: `Use my referral code ${user.referralCode} to get started with AuraESIM!`,
        url: `https://auraesim.com/ref/${user.referralCode}`,
      });
    } else {
      // Fallback for browsers that don't support Web Share API
      handleCopyReferralCode();
    }
  };

  const handleTopUp = () => {
    // Navigate to top-up page
    console.log("Navigate to top-up");
  };

  const handleViewTransactions = () => {
    // Navigate to transaction history
    console.log("Navigate to transactions");
  };

  const handleEditProfile = () => {
    // Navigate to edit profile page
    console.log("Navigate to edit profile");
  };

  return (
    <MainLayout>
      <div className="space-y-6 p-4">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold">Profile</h1>
          <p className="text-muted-foreground">Manage your account and preferences</p>
        </div>

        {/* User Profile Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-lg">
                  {user.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-lg font-semibold">{user.name}</h3>
                <p className="text-sm text-muted-foreground">{user.email}</p>
                {user.phone && (
                  <p className="text-sm text-muted-foreground">{user.phone}</p>
                )}
              </div>
              <Button variant="outline" size="sm" onClick={handleEditProfile}>
                <User className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Account Balance */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              <Wallet className="h-5 w-5" />
              Account Balance
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">${user.balance.toFixed(2)}</div>
                <p className="text-sm text-muted-foreground">Available balance</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={handleTopUp}>
                  <Plus className="h-4 w-4 mr-2" />
                  Top Up
                </Button>
                <Button variant="outline" size="sm" onClick={handleViewTransactions}>
                  View Details
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Referral Code */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              <Gift className="h-5 w-5" />
              Referral Code
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="font-mono text-lg font-semibold">{user.referralCode}</div>
                  <p className="text-sm text-muted-foreground">
                    Share with friends to earn rewards
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleCopyReferralCode}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleShareReferralCode}>
                    <Share className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge variant="secondary">Earn $5 per referral</Badge>
                <Badge variant="outline">Friend gets $5 credit</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Menu Items */}
        <Card>
          <CardContent className="p-0">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <div key={item.title}>
                  <button
                    className="flex items-center gap-3 w-full p-4 text-left hover:bg-muted/50 transition-colors"
                    onClick={() => console.log(`Navigate to ${item.href}`)}
                  >
                    <Icon className="h-5 w-5 text-muted-foreground" />
                    <div className="flex-1">
                      <div className="font-medium">{item.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.description}
                      </div>
                    </div>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </button>
                  {index < menuItems.length - 1 && <Separator />}
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* App Version */}
        <div className="text-center text-sm text-muted-foreground">
          AuraESIM v1.0.0
        </div>
      </div>
    </MainLayout>
  );
}
