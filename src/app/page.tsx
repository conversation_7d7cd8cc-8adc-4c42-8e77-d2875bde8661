"use client";

import { useState } from "react";
import { Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MainLayout } from "@/components/main-layout";
import { SearchDialog } from "@/components/search-dialog";
import { HeroCarousel } from "@/components/hero-carousel";
import { RecommendedRegions } from "@/components/recommended-regions";
import { PlanCard } from "@/components/plan-card";
import type { ESIMPlan } from "@/types";

// Mock data for demonstration
const mockPlans: ESIMPlan[] = [
  {
    id: "1",
    name: "USA Unlimited",
    description: "Unlimited high-speed data with 5G coverage across the United States",
    region: "North America",
    countries: ["US"],
    dataAmount: 50,
    validityDays: 30,
    price: 29.99,
    currency: "USD",
    isHot: true,
    networkTypes: ["4G", "5G"],
    carriers: ["Verizon", "AT&T", "T-Mobile"],
    coverageAreas: ["United States"],
  },
  {
    id: "2",
    name: "Europe Multi-Country",
    description: "30GB data valid across 30+ European countries",
    region: "Europe",
    countries: ["DE", "FR", "IT", "ES", "UK"],
    dataAmount: 30,
    validityDays: 30,
    price: 24.99,
    currency: "USD",
    isHot: true,
    networkTypes: ["4G", "5G"],
    carriers: ["Vodafone", "Orange", "T-Mobile"],
    coverageAreas: ["Europe"],
  },
  {
    id: "3",
    name: "Japan Travel",
    description: "20GB high-speed data for Japan with 5G support",
    region: "Asia",
    countries: ["JP"],
    dataAmount: 20,
    validityDays: 15,
    price: 19.99,
    currency: "USD",
    networkTypes: ["4G", "5G"],
    carriers: ["NTT Docomo", "SoftBank", "KDDI"],
    coverageAreas: ["Japan"],
  },
  {
    id: "4",
    name: "Global Roaming",
    description: "50GB data valid in 100+ countries worldwide",
    region: "Global",
    countries: ["US", "UK", "JP", "CN", "DE", "FR", "IT", "ES", "KR", "TH"],
    dataAmount: 50,
    validityDays: 30,
    price: 49.99,
    currency: "USD",
    isGlobal: true,
    networkTypes: ["4G", "5G"],
    carriers: ["Multiple carriers"],
    coverageAreas: ["Worldwide"],
  },
];

export default function Home() {
  const [searchOpen, setSearchOpen] = useState(false);

  const handlePlanSelect = (plan: ESIMPlan) => {
    // Handle plan selection - navigate to plan details or purchase flow
    console.log('Selected plan:', plan.id);
  };

  return (
    <MainLayout>
      <div className="space-y-6 p-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">AuraESIM</h1>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSearchOpen(true)}
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>

        {/* Hero Carousel */}
        <HeroCarousel />

        {/* Recommended Regions */}
        <RecommendedRegions />

        {/* Featured Plans */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Featured Plans</h2>
            <button className="text-sm text-primary hover:underline">
              View All
            </button>
          </div>

          <div className="grid gap-4">
            {mockPlans.map((plan) => (
              <PlanCard
                key={plan.id}
                plan={plan}
                onSelect={handlePlanSelect}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Search Dialog */}
      <SearchDialog
        open={searchOpen}
        onOpenChange={setSearchOpen}
      />
    </MainLayout>
  );
}
