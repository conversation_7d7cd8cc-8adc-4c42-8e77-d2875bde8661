"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Search, HelpCircle } from "lucide-react";

interface FAQ {
  id: string;
  category: string;
  question: string;
  answer: string;
}

const mockFAQs: FAQ[] = [
  {
    id: "1",
    category: "Getting Started",
    question: "What is an eSIM?",
    answer: "An eSIM (embedded SIM) is a digital SIM card that allows you to connect to a mobile network without needing a physical SIM card. It's built into your device and can be programmed remotely to connect to different carriers."
  },
  {
    id: "2",
    category: "Getting Started",
    question: "How do I know if my device supports eSIM?",
    answer: "Most modern smartphones support eSIM, including iPhone XS and newer, Google Pixel 3 and newer, Samsung Galaxy S20 and newer. You can check your device settings or contact your device manufacturer to confirm eSIM support."
  },
  {
    id: "3",
    category: "Purchasing",
    question: "How do I buy an eSIM plan?",
    answer: "Simply browse our available plans, select the one that fits your needs, complete the payment process, and you'll receive a QR code to activate your eSIM. The entire process takes just a few minutes."
  },
  {
    id: "4",
    category: "Purchasing",
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards (Visa, Mastercard, American Express), PayPal, Apple Pay, and Google Pay. All payments are processed securely through our encrypted payment system."
  },
  {
    id: "5",
    category: "Activation",
    question: "How do I activate my eSIM?",
    answer: "After purchase, you'll receive a QR code via email. Go to your device's cellular settings, select 'Add Cellular Plan', and scan the QR code. Follow the on-screen instructions to complete activation."
  },
  {
    id: "6",
    category: "Activation",
    question: "When should I activate my eSIM?",
    answer: "Activate your eSIM when you arrive at your destination or when you're ready to start using the data. The validity period starts from the moment of activation, not from the time of purchase."
  },
  {
    id: "7",
    category: "Usage",
    question: "Can I use my eSIM for calls and SMS?",
    answer: "Our eSIM plans are data-only and don't include voice calls or SMS. However, you can use internet-based calling and messaging apps like WhatsApp, Skype, or FaceTime over the data connection."
  },
  {
    id: "8",
    category: "Usage",
    question: "What happens when I run out of data?",
    answer: "When you reach your data limit, your connection will be suspended. You can purchase additional data or a new plan through our app to continue using the service."
  },
  {
    id: "9",
    category: "Technical",
    question: "Can I use multiple eSIMs at the same time?",
    answer: "Yes, if your device supports dual SIM functionality, you can have multiple eSIM profiles installed and switch between them as needed. However, only one can be active for data at a time."
  },
  {
    id: "10",
    category: "Technical",
    question: "What should I do if my eSIM isn't working?",
    answer: "First, ensure you're in an area with network coverage and that your device is compatible. Try restarting your device and checking your cellular settings. If issues persist, contact our support team for assistance."
  }
];

const categories = Array.from(new Set(mockFAQs.map(faq => faq.category)));

export default function FAQPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredFAQs = mockFAQs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">FAQ</h1>
            <p className="text-sm text-muted-foreground">Frequently Asked Questions</p>
          </div>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search questions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Categories */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            <Button
              variant={selectedCategory === null ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(null)}
            >
              All
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="whitespace-nowrap"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* FAQ List */}
          {filteredFAQs.length > 0 ? (
            <Card>
              <CardContent className="p-0">
                <Accordion type="single" collapsible className="w-full">
                  {filteredFAQs.map((faq, index) => (
                    <AccordionItem key={faq.id} value={faq.id} className="border-b last:border-b-0">
                      <AccordionTrigger className="px-4 py-4 text-left hover:no-underline">
                        <div className="flex items-start gap-3 text-left">
                          <HelpCircle className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                          <div className="space-y-1">
                            <div className="font-medium">{faq.question}</div>
                            <Badge variant="secondary" className="text-xs">
                              {faq.category}
                            </Badge>
                          </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-4 pb-4">
                        <div className="pl-7 text-sm text-muted-foreground leading-relaxed">
                          {faq.answer}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ) : (
            <div className="text-center py-12">
              <HelpCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No results found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search or browse different categories.
              </p>
              <Button variant="outline" onClick={() => {
                setSearchQuery("");
                setSelectedCategory(null);
              }}>
                Clear filters
              </Button>
            </div>
          )}

          {/* Contact Support */}
          <Card>
            <CardContent className="p-4 text-center">
              <h3 className="font-medium mb-2">Still need help?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Can't find what you're looking for? Our support team is here to help.
              </p>
              <Button onClick={() => router.push('/contact')}>
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
