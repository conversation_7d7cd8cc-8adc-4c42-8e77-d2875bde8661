"use client";

import { useState } from "react";
import { MainLayout } from "@/components/main-layout";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Wifi, Clock, MoreVertical } from "lucide-react";
import type { ActiveESIM } from "@/types";

// Mock data for demonstration
const mockActiveESIMs: ActiveESIM[] = [
  {
    id: "1",
    planId: "1",
    plan: {
      id: "1",
      name: "USA Unlimited",
      description: "Unlimited high-speed data",
      region: "North America",
      countries: ["US"],
      dataAmount: 50,
      validityDays: 30,
      price: 29.99,
      currency: "USD",
      networkTypes: ["4G", "5G"],
      carriers: ["Verizon", "AT&T"],
      coverageAreas: ["United States"],
    },
    userId: "user1",
    orderId: "order1",
    status: "active",
    dataUsed: 15.5,
    dataRemaining: 34.5,
    activatedAt: "2024-01-15T10:00:00Z",
    expiresAt: "2024-02-14T10:00:00Z",
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-20T15:30:00Z",
  },
  {
    id: "2",
    planId: "2",
    plan: {
      id: "2",
      name: "Europe Multi-Country",
      description: "30GB across 30+ countries",
      region: "Europe",
      countries: ["DE", "FR", "IT", "ES", "UK"],
      dataAmount: 30,
      validityDays: 30,
      price: 24.99,
      currency: "USD",
      networkTypes: ["4G", "5G"],
      carriers: ["Vodafone", "Orange"],
      coverageAreas: ["Europe"],
    },
    userId: "user1",
    orderId: "order2",
    status: "active",
    dataUsed: 8.2,
    dataRemaining: 21.8,
    activatedAt: "2024-01-10T14:00:00Z",
    expiresAt: "2024-02-09T14:00:00Z",
    createdAt: "2024-01-10T13:00:00Z",
    updatedAt: "2024-01-20T12:15:00Z",
  },
];

const mockExpiredESIMs: ActiveESIM[] = [
  {
    id: "3",
    planId: "3",
    plan: {
      id: "3",
      name: "Japan Travel",
      description: "20GB for Japan",
      region: "Asia",
      countries: ["JP"],
      dataAmount: 20,
      validityDays: 15,
      price: 19.99,
      currency: "USD",
      networkTypes: ["4G", "5G"],
      carriers: ["NTT Docomo"],
      coverageAreas: ["Japan"],
    },
    userId: "user1",
    orderId: "order3",
    status: "expired",
    dataUsed: 18.5,
    dataRemaining: 1.5,
    activatedAt: "2023-12-01T10:00:00Z",
    expiresAt: "2023-12-16T10:00:00Z",
    createdAt: "2023-12-01T09:00:00Z",
    updatedAt: "2023-12-16T10:00:00Z",
  },
];

function ESIMCard({ esim, onViewDetails }: { esim: ActiveESIM; onViewDetails: (esim: ActiveESIM) => void }) {
  const dataUsagePercentage = (esim.dataUsed / esim.plan.dataAmount) * 100;
  const isExpired = esim.status === 'expired';
  
  const getRemainingDays = () => {
    const now = new Date();
    const expiresAt = new Date(esim.expiresAt);
    const diffTime = expiresAt.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const getCountryFlag = (countryCode: string): string => {
    const flagMap: Record<string, string> = {
      'US': '🇺🇸',
      'UK': '🇬🇧',
      'JP': '🇯🇵',
      'CN': '🇨🇳',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
    };
    return flagMap[countryCode] || '🌍';
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onViewDetails(esim)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl">
                {esim.plan.countries.length === 1 ? 
                  getCountryFlag(esim.plan.countries[0]) : 
                  '🌍'
                }
              </span>
              <div>
                <h3 className="font-medium">{esim.plan.name}</h3>
                <p className="text-sm text-muted-foreground">{esim.plan.region}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isExpired ? "secondary" : "default"}>
                {isExpired ? "Expired" : "Active"}
              </Badge>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Data Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-1">
                <Wifi className="h-4 w-4" />
                Data Usage
              </span>
              <span className="font-medium">
                {esim.dataUsed}GB / {esim.plan.dataAmount}GB
              </span>
            </div>
            <Progress value={dataUsagePercentage} className="h-2" />
          </div>

          {/* Remaining Time */}
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {isExpired ? "Expired" : "Remaining"}
            </span>
            <span className="font-medium">
              {isExpired ? "0 days" : `${getRemainingDays()} days`}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function ESIMsPage() {
  const [activeTab, setActiveTab] = useState("active");

  const handleViewDetails = (esim: ActiveESIM) => {
    // Navigate to eSIM details page
    console.log('View details for eSIM:', esim.id);
  };

  return (
    <MainLayout>
      <div className="space-y-6 p-4">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold">My eSIMs</h1>
          <p className="text-muted-foreground">Manage your active and expired eSIM plans</p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="active">Active ({mockActiveESIMs.length})</TabsTrigger>
            <TabsTrigger value="expired">Expired ({mockExpiredESIMs.length})</TabsTrigger>
          </TabsList>
          
          <TabsContent value="active" className="space-y-4 mt-6">
            {mockActiveESIMs.length > 0 ? (
              <div className="space-y-4">
                {mockActiveESIMs.map((esim) => (
                  <ESIMCard
                    key={esim.id}
                    esim={esim}
                    onViewDetails={handleViewDetails}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Wifi className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Active eSIMs</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any active eSIM plans yet.
                </p>
                <Button>Browse Plans</Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="expired" className="space-y-4 mt-6">
            {mockExpiredESIMs.length > 0 ? (
              <div className="space-y-4">
                {mockExpiredESIMs.map((esim) => (
                  <ESIMCard
                    key={esim.id}
                    esim={esim}
                    onViewDetails={handleViewDetails}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Expired eSIMs</h3>
                <p className="text-muted-foreground">
                  Your expired eSIM plans will appear here.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
