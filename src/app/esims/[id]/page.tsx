"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Wifi, 
  Clock, 
  Globe, 
  CreditCard, 
  Calendar,
  MapPin,
  Signal,
  Plus
} from "lucide-react";
import type { ActiveESIM } from "@/types";

// Mock data - in real app, this would be fetched based on the ID
const mockESIM: ActiveESIM = {
  id: "1",
  planId: "1",
  plan: {
    id: "1",
    name: "USA Unlimited",
    description: "Unlimited high-speed data with 5G coverage across the United States. Perfect for business travelers and tourists who need reliable connectivity.",
    region: "North America",
    countries: ["US"],
    dataAmount: 50,
    validityDays: 30,
    price: 29.99,
    currency: "USD",
    isHot: true,
    networkTypes: ["4G", "5G"],
    carriers: ["Verizon", "AT&T", "T-Mobile"],
    coverageAreas: ["United States", "Puerto Rico", "US Virgin Islands"],
  },
  userId: "user1",
  orderId: "ORD-2024-001",
  status: "active",
  dataUsed: 15.5,
  dataRemaining: 34.5,
  activatedAt: "2024-01-15T10:00:00Z",
  expiresAt: "2024-02-14T10:00:00Z",
  createdAt: "2024-01-15T09:00:00Z",
  updatedAt: "2024-01-20T15:30:00Z",
};

export default function ESIMDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [esim] = useState(mockESIM);

  const dataUsagePercentage = (esim.dataUsed / esim.plan.dataAmount) * 100;
  
  const getRemainingDays = () => {
    const now = new Date();
    const expiresAt = new Date(esim.expiresAt);
    const diffTime = expiresAt.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleTopUp = () => {
    // Navigate to top-up flow
    console.log('Navigate to top-up for eSIM:', esim.id);
  };

  const getCountryFlag = (countryCode: string): string => {
    const flagMap: Record<string, string> = {
      'US': '🇺🇸',
      'UK': '🇬🇧',
      'JP': '🇯🇵',
      'CN': '🇨🇳',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
    };
    return flagMap[countryCode] || '🌍';
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-3">
            <span className="text-2xl">
              {getCountryFlag(esim.plan.countries[0])}
            </span>
            <div>
              <h1 className="text-xl font-bold">{esim.plan.name}</h1>
              <p className="text-sm text-muted-foreground">{esim.plan.region}</p>
            </div>
          </div>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* Usage Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                Usage Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {esim.dataRemaining}GB
                  </div>
                  <div className="text-sm text-muted-foreground">Remaining</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {getRemainingDays()}
                  </div>
                  <div className="text-sm text-muted-foreground">Days left</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Data Usage</span>
                  <span>{esim.dataUsed}GB / {esim.plan.dataAmount}GB</span>
                </div>
                <Progress value={dataUsagePercentage} className="h-2" />
              </div>

              <Button className="w-full" onClick={handleTopUp}>
                <Plus className="h-4 w-4 mr-2" />
                Top Up Data
              </Button>
            </CardContent>
          </Card>

          {/* Order Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Order Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Order ID</span>
                <span className="font-mono">{esim.orderId}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Purchase Date</span>
                <span>{formatDate(esim.createdAt)}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Activated</span>
                <span>{esim.activatedAt ? formatDate(esim.activatedAt) : 'Not activated'}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Expires</span>
                <span>{formatDate(esim.expiresAt)}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Amount Paid</span>
                <span className="font-semibold">${esim.plan.price} {esim.plan.currency}</span>
              </div>
            </CardContent>
          </Card>

          {/* Plan Description */}
          <Card>
            <CardHeader>
              <CardTitle>Plan Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {esim.plan.description}
              </p>
            </CardContent>
          </Card>

          {/* Coverage & Network */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Signal className="h-5 w-5" />
                Coverage & Network
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Coverage Areas
                </h4>
                <div className="flex flex-wrap gap-2">
                  {esim.plan.coverageAreas.map((area) => (
                    <Badge key={area} variant="outline">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Supported Carriers
                </h4>
                <div className="flex flex-wrap gap-2">
                  {esim.plan.carriers.map((carrier) => (
                    <Badge key={carrier} variant="secondary">
                      {carrier}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Network Types</h4>
                <div className="flex gap-2">
                  {esim.plan.networkTypes.map((network) => (
                    <Badge key={network} variant="default">
                      {network}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
