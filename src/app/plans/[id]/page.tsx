"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Wifi, 
  Clock, 
  Globe, 
  MapPin,
  Signal,
  ShoppingCart,
  Star,
  Check
} from "lucide-react";
import type { ESIMPlan } from "@/types";

// Mock plan data - in real app, this would be fetched based on the ID
const mockPlan: ESIMPlan = {
  id: "1",
  name: "USA Unlimited",
  description: "Unlimited high-speed data with 5G coverage across the United States. Perfect for business travelers and tourists who need reliable connectivity throughout their stay.",
  region: "North America",
  countries: ["US"],
  dataAmount: 50,
  validityDays: 30,
  price: 29.99,
  currency: "USD",
  isHot: true,
  networkTypes: ["4G", "5G"],
  carriers: ["Verizon", "AT&T", "T-Mobile"],
  coverageAreas: ["United States", "Puerto Rico", "US Virgin Islands"],
};

const features = [
  "Instant activation via QR code",
  "No roaming charges",
  "Keep your original number",
  "24/7 customer support",
  "No contract required",
  "Works with unlocked devices",
];

const reviews = [
  {
    id: "1",
    user: "Sarah M.",
    rating: 5,
    comment: "Excellent coverage throughout my trip to New York. Fast speeds and easy setup!",
    date: "2024-01-15"
  },
  {
    id: "2", 
    user: "Mike R.",
    rating: 5,
    comment: "Worked perfectly in Los Angeles and San Francisco. Highly recommend!",
    date: "2024-01-10"
  },
  {
    id: "3",
    user: "Emma L.",
    rating: 4,
    comment: "Good speeds overall, had some issues in rural areas but great in cities.",
    date: "2024-01-05"
  }
];

export default function PlanDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [plan] = useState(mockPlan);

  const formatDataAmount = (amount: number) => {
    if (amount >= 1000) {
      return `${amount / 1000}TB`;
    }
    return `${amount}GB`;
  };

  const formatValidityDays = (days: number) => {
    if (days >= 30) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      if (remainingDays === 0) {
        return `${months} month${months > 1 ? 's' : ''}`;
      }
      return `${months}m ${remainingDays}d`;
    }
    return `${days} day${days > 1 ? 's' : ''}`;
  };

  const getCountryFlag = (countryCode: string): string => {
    const flagMap: Record<string, string> = {
      'US': '🇺🇸',
      'UK': '🇬🇧',
      'JP': '🇯🇵',
      'CN': '🇨🇳',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
    };
    return flagMap[countryCode] || '🌍';
  };

  const handlePurchase = () => {
    // Navigate to purchase flow
    console.log('Navigate to purchase for plan:', plan.id);
  };

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-3">
            <span className="text-2xl">
              {getCountryFlag(plan.countries[0])}
            </span>
            <div>
              <h1 className="text-xl font-bold">{plan.name}</h1>
              <p className="text-sm text-muted-foreground">{plan.region}</p>
            </div>
          </div>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* Plan Overview */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="text-3xl font-bold">${plan.price}</div>
                  {plan.isHot && (
                    <Badge variant="destructive">HOT</Badge>
                  )}
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="space-y-1">
                    <Wifi className="h-5 w-5 mx-auto text-muted-foreground" />
                    <div className="font-medium">{formatDataAmount(plan.dataAmount)}</div>
                    <div className="text-xs text-muted-foreground">Data</div>
                  </div>
                  <div className="space-y-1">
                    <Clock className="h-5 w-5 mx-auto text-muted-foreground" />
                    <div className="font-medium">{formatValidityDays(plan.validityDays)}</div>
                    <div className="text-xs text-muted-foreground">Validity</div>
                  </div>
                  <div className="space-y-1">
                    <Globe className="h-5 w-5 mx-auto text-muted-foreground" />
                    <div className="font-medium">{plan.countries.length}</div>
                    <div className="text-xs text-muted-foreground">
                      {plan.countries.length === 1 ? 'Country' : 'Countries'}
                    </div>
                  </div>
                </div>

                <Button className="w-full" size="lg" onClick={handlePurchase}>
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Buy Now - ${plan.price}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Plan Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {plan.description}
              </p>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>What's Included</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Coverage & Network */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Signal className="h-5 w-5" />
                Coverage & Network
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Coverage Areas
                </h4>
                <div className="flex flex-wrap gap-2">
                  {plan.coverageAreas.map((area) => (
                    <Badge key={area} variant="outline">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Supported Carriers</h4>
                <div className="flex flex-wrap gap-2">
                  {plan.carriers.map((carrier) => (
                    <Badge key={carrier} variant="secondary">
                      {carrier}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Network Types</h4>
                <div className="flex gap-2">
                  {plan.networkTypes.map((network) => (
                    <Badge key={network} variant="default">
                      {network}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reviews */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Customer Reviews
                <Badge variant="secondary" className="ml-auto">
                  {averageRating.toFixed(1)} ★
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reviews.map((review, index) => (
                  <div key={review.id}>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="font-medium">{review.user}</div>
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < review.rating
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">{review.comment}</p>
                      <div className="text-xs text-muted-foreground">
                        {new Date(review.date).toLocaleDateString()}
                      </div>
                    </div>
                    {index < reviews.length - 1 && <Separator className="mt-4" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
