"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ArrowLeft } from "lucide-react";

interface PlanOption {
  id: string;
  dataAmount: number;
  validityDays: number;
  price: number;
  currency: string;
  isRecommended?: boolean;
}

// Mock data based on country
const mockPlans: Record<string, { name: string; flag: string; plans: PlanOption[] }> = {
  "us": {
    name: "美国",
    flag: "🇺🇸",
    plans: [
      {
        id: "us-1gb-7d",
        dataAmount: 1,
        validityDays: 7,
        price: 3.99,
        currency: "USD",
      },
      {
        id: "us-3gb-30d",
        dataAmount: 3,
        validityDays: 30,
        price: 8.99,
        currency: "USD",
      },
      {
        id: "us-5gb-30d",
        dataAmount: 5,
        validityDays: 30,
        price: 13.99,
        currency: "USD",
        isRecommended: true,
      },
      {
        id: "us-10gb-30d",
        dataAmount: 10,
        validityDays: 30,
        price: 22.99,
        currency: "USD",
      },
      {
        id: "us-20gb-30d",
        dataAmount: 20,
        validityDays: 30,
        price: 36.99,
        currency: "USD",
      },
      {
        id: "us-unlimited-15d",
        dataAmount: 999,
        validityDays: 15,
        price: 49.99,
        currency: "USD",
      },
    ]
  }
};

export default function CountryPlansPage() {
  const params = useParams();
  const router = useRouter();
  const country = params.country as string;
  const [selectedPlan, setSelectedPlan] = useState<string>("");

  const countryData = mockPlans[country];
  
  if (!countryData) {
    return (
      <MainLayout showBottomNav={false}>
        <div className="p-4 text-center">
          <h1>Country not found</h1>
        </div>
      </MainLayout>
    );
  }

  const formatDataAmount = (amount: number) => {
    if (amount >= 999) {
      return "无限流量";
    }
    return `${amount} GB`;
  };

  const formatValidityDays = (days: number) => {
    return `${days} 天`;
  };

  const handleContinue = () => {
    if (selectedPlan) {
      // Navigate to checkout with selected plan
      router.push(`/checkout?plan=${selectedPlan}`);
    }
  };

  // Set default selection to recommended plan
  useState(() => {
    const recommendedPlan = countryData.plans.find(plan => plan.isRecommended);
    if (recommendedPlan) {
      setSelectedPlan(recommendedPlan.id);
    }
  });

  return (
    <MainLayout showBottomNav={false}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="flex items-center gap-3 p-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="rounded-full"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <span className="text-lg">流量套餐</span>
              <span className="text-lg font-medium">{countryData.name}</span>
            </div>
          </div>
        </div>

        <div className="p-4 space-y-4">
          {/* Country Header */}
          <div className="flex items-center gap-3 bg-white rounded-lg p-4 shadow-sm">
            <span className="text-2xl">{countryData.flag}</span>
            <span className="font-medium">可用套餐</span>
          </div>

          {/* Plans */}
          <RadioGroup value={selectedPlan} onValueChange={setSelectedPlan} className="space-y-3">
            {countryData.plans.map((plan) => (
              <div key={plan.id} className="relative">
                {plan.isRecommended && (
                  <div className="absolute -top-2 left-4 right-4 z-10">
                    <div className="bg-black text-white text-center py-1 rounded-t-lg text-sm font-medium">
                      最佳选择
                    </div>
                  </div>
                )}
                <Card 
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedPlan === plan.id 
                      ? 'ring-2 ring-black bg-white' 
                      : 'bg-white hover:shadow-md'
                  } ${plan.isRecommended ? 'border-2 border-black' : 'border border-gray-200'}`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <RadioGroupItem 
                          value={plan.id} 
                          id={plan.id}
                          className="border-gray-300"
                        />
                        <Label htmlFor={plan.id} className="cursor-pointer">
                          <div className="space-y-1">
                            <div className="font-medium text-lg">
                              {formatDataAmount(plan.dataAmount)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {formatValidityDays(plan.validityDays)}
                            </div>
                          </div>
                        </Label>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-lg">
                          {plan.currency} {plan.price.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Continue Button */}
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
          <Button 
            className="w-full h-12 text-base font-medium rounded-xl"
            onClick={handleContinue}
            disabled={!selectedPlan}
          >
            继续
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
