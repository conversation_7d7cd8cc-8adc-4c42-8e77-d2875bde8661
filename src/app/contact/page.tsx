"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Mail, 
  MessageCircle, 
  Phone, 
  Clock,
  Send,
  ExternalLink
} from "lucide-react";
import { toast } from "sonner";

const contactMethods = [
  {
    icon: MessageCircle,
    title: "Live Chat",
    description: "Get instant help from our support team",
    action: "Start Chat",
    available: "24/7",
  },
  {
    icon: Mail,
    title: "Email Support",
    description: "Send us a detailed message",
    action: "<EMAIL>",
    available: "Response within 24h",
  },
  {
    icon: Phone,
    title: "Phone Support",
    description: "Speak directly with our team",
    action: "+****************",
    available: "Mon-Fri 9AM-6PM EST",
  },
];

const issueTypes = [
  "Technical Issue",
  "Billing Question",
  "Plan Information",
  "Activation Help",
  "Refund Request",
  "Feature Request",
  "Other",
];

export default function ContactPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    issueType: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Simulate form submission
    toast.success("Message sent successfully! We'll get back to you soon.");
    
    // Reset form
    setFormData({
      name: "",
      email: "",
      issueType: "",
      subject: "",
      message: "",
    });
  };

  const handleContactMethod = (method: typeof contactMethods[0]) => {
    if (method.title === "Live Chat") {
      // Open live chat widget
      toast.info("Live chat feature coming soon!");
    } else if (method.title === "Email Support") {
      window.location.href = `mailto:${method.action}`;
    } else if (method.title === "Phone Support") {
      window.location.href = `tel:${method.action}`;
    }
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Contact Us</h1>
            <p className="text-sm text-muted-foreground">We're here to help</p>
          </div>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* Quick Contact Methods */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Get Help Quickly</h2>
            <div className="grid gap-4">
              {contactMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <Card key={method.title} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{method.title}</h3>
                          <p className="text-sm text-muted-foreground">{method.description}</p>
                          <div className="flex items-center gap-1 mt-1">
                            <Clock className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">{method.available}</span>
                          </div>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleContactMethod(method)}
                        >
                          {method.title === "Email Support" || method.title === "Phone Support" ? (
                            <ExternalLink className="h-4 w-4" />
                          ) : (
                            method.action
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Send us a Message
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="issueType">Issue Type</Label>
                  <Select value={formData.issueType} onValueChange={(value) => handleInputChange("issueType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an issue type" />
                    </SelectTrigger>
                    <SelectContent>
                      {issueTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    value={formData.subject}
                    onChange={(e) => handleInputChange("subject", e.target.value)}
                    placeholder="Brief description of your issue"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) => handleInputChange("message", e.target.value)}
                    placeholder="Please provide as much detail as possible..."
                    rows={5}
                    required
                  />
                </div>

                <Button type="submit" className="w-full">
                  <Send className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <Card>
            <CardContent className="p-4">
              <div className="text-center space-y-2">
                <h3 className="font-medium">Response Time</h3>
                <p className="text-sm text-muted-foreground">
                  We typically respond to messages within 24 hours during business days.
                  For urgent issues, please use our live chat or phone support.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
