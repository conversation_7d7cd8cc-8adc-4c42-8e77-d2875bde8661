"use client";

import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Globe, 
  Shield, 
  Zap, 
  Users, 
  ExternalLink,
  Heart
} from "lucide-react";

const features = [
  {
    icon: Globe,
    title: "Global Coverage",
    description: "Connect in 100+ countries worldwide with our extensive network partnerships"
  },
  {
    icon: Zap,
    title: "Instant Activation",
    description: "Get connected in minutes with our simple QR code activation process"
  },
  {
    icon: Shield,
    title: "Secure & Reliable",
    description: "Enterprise-grade security with 99.9% network uptime guarantee"
  },
  {
    icon: Users,
    title: "24/7 Support",
    description: "Our dedicated support team is always here to help you stay connected"
  }
];

const legalLinks = [
  { title: "Privacy Policy", href: "/privacy" },
  { title: "Terms of Service", href: "/terms" },
  { title: "Cookie Policy", href: "/cookies" },
  { title: "Data Protection", href: "/data-protection" },
];

export default function AboutPage() {
  const router = useRouter();

  const handleLegalLink = (href: string) => {
    // In a real app, these would navigate to actual legal pages
    console.log(`Navigate to ${href}`);
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">About AuraESIM</h1>
            <p className="text-sm text-muted-foreground">Your global connectivity partner</p>
          </div>
        </div>

        <div className="space-y-6 p-4 pt-0">
          {/* App Info */}
          <Card>
            <CardContent className="p-6 text-center">
              <div className="space-y-4">
                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold">AuraESIM</h2>
                  <p className="text-muted-foreground">Version 1.0.0</p>
                </div>
                <Badge variant="secondary">Global eSIM Provider</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Mission */}
          <Card>
            <CardHeader>
              <CardTitle>Our Mission</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                At AuraESIM, we believe that staying connected while traveling should be simple, 
                affordable, and reliable. We're dedicated to providing seamless global connectivity 
                through innovative eSIM technology, empowering travelers to explore the world 
                without worrying about connectivity.
              </p>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>Why Choose AuraESIM?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div key={feature.title}>
                      <div className="flex gap-3">
                        <div className="p-2 rounded-lg bg-primary/10 flex-shrink-0">
                          <Icon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium">{feature.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                      {index < features.length - 1 && <Separator className="my-4" />}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Company Info */}
          <Card>
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Founded</span>
                <span>2024</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Headquarters</span>
                <span>San Francisco, CA</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Countries Served</span>
                <span>100+</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">Active Users</span>
                <span>1M+</span>
              </div>
            </CardContent>
          </Card>

          {/* Legal Links */}
          <Card>
            <CardHeader>
              <CardTitle>Legal & Privacy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2">
                {legalLinks.map((link, index) => (
                  <div key={link.title}>
                    <button
                      className="flex items-center justify-between w-full p-2 text-left hover:bg-muted/50 rounded-lg transition-colors"
                      onClick={() => handleLegalLink(link.href)}
                    >
                      <span>{link.title}</span>
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    </button>
                    {index < legalLinks.length - 1 && <Separator className="my-1" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Contact & Social */}
          <Card>
            <CardHeader>
              <CardTitle>Connect With Us</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Website</span>
                  <button className="text-primary hover:underline">
                    www.auraesim.com
                  </button>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Support Email</span>
                  <button className="text-primary hover:underline">
                    <EMAIL>
                  </button>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Twitter</span>
                  <button className="text-primary hover:underline">
                    @AuraESIM
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <span>Made with</span>
                <Heart className="h-4 w-4 text-red-500 fill-current" />
                <span>for travelers worldwide</span>
              </div>
              <div className="text-xs text-muted-foreground mt-2">
                © 2024 AuraESIM. All rights reserved.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
