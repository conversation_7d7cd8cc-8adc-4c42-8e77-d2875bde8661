"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { X, Search, ArrowRight } from "lucide-react";

interface SearchResult {
  id: string;
  name: string;
  flag: string;
  type: 'country' | 'region';
  startingPrice: number;
  currency: string;
  description?: string;
}

const mockSearchResults: SearchResult[] = [
  { id: "tr", name: "土耳其", flag: "🇹🇷", type: "country", startingPrice: 3.99, currency: "USD" },
  { id: "us", name: "美国", flag: "🇺🇸", type: "country", startingPrice: 3.99, currency: "USD" },
  { id: "th", name: "泰国", flag: "🇹🇭", type: "country", startingPrice: 2.99, currency: "USD" },
  { id: "my", name: "马来西亚", flag: "🇲🇾", type: "country", startingPrice: 3.99, currency: "USD" },
  { id: "ae", name: "阿拉伯联合酋长国", flag: "🇦🇪", type: "country", startingPrice: 3.99, currency: "USD" },
  { id: "tl", name: "东帝汶", flag: "🇹🇱", type: "country", startingPrice: 19.99, currency: "USD" },
  { id: "cn", name: "中国", flag: "🇨🇳", type: "country", startingPrice: 2.99, currency: "USD" },
];

const regionResults: SearchResult[] = [
  { id: "global", name: "全球", flag: "🌍", type: "region", startingPrice: 8.99, currency: "USD", description: "113 个国家/地区" },
  { id: "europe", name: "欧洲", flag: "🇪🇺", type: "region", startingPrice: 4.99, currency: "USD", description: "35 个国家/地区" },
  { id: "asia-pacific", name: "亚洲和大洋洲", flag: "🌏", type: "region", startingPrice: 4.99, currency: "USD", description: "19 个国家/地区" },
  { id: "north-america", name: "北美洲", flag: "🌎", type: "region", startingPrice: 5.99, currency: "USD", description: "2 个国家/地区" },
  { id: "middle-east-africa", name: "中东和北非", flag: "🌍", type: "region", startingPrice: 13.99, currency: "USD", description: "19 个国家/地区" },
  { id: "latin-america", name: "拉丁美洲", flag: "🌎", type: "region", startingPrice: 13.99, currency: "USD", description: "12 个国家/地区" },
  { id: "africa", name: "非洲", flag: "🌍", type: "region", startingPrice: 26.49, currency: "USD", description: "34 个国家/地区" },
];

export default function SearchPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<'country' | 'region'>('country');
  const [filteredResults, setFilteredResults] = useState<SearchResult[]>(mockSearchResults);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    const results = activeTab === 'country' ? mockSearchResults : regionResults;
    if (query.trim()) {
      const filtered = results.filter(item =>
        item.name.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredResults(filtered);
    } else {
      setFilteredResults(results);
    }
  };

  const handleTabChange = (tab: 'country' | 'region') => {
    setActiveTab(tab);
    const results = tab === 'country' ? mockSearchResults : regionResults;
    setFilteredResults(results);
    setSearchQuery("");
  };

  const handleResultClick = (result: SearchResult) => {
    if (result.type === 'country') {
      router.push(`/plans/country/${result.id}`);
    } else {
      router.push(`/plans/region/${result.id}`);
    }
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="flex items-center gap-3 p-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="rounded-full"
            >
              <X className="h-5 w-5" />
            </Button>
            <span className="text-lg font-medium">流量套餐</span>
          </div>

          {/* Search Input */}
          <div className="px-4 pb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="搜索"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 bg-gray-100 border-0 rounded-lg"
              />
            </div>
          </div>

          {/* Tabs */}
          <div className="flex px-4 pb-4">
            <Button
              variant={activeTab === 'country' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleTabChange('country')}
              className="rounded-full mr-2"
            >
              国家/地区
            </Button>
            <Button
              variant={activeTab === 'region' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleTabChange('region')}
              className="rounded-full"
            >
              区域
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {activeTab === 'country' && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-600">热门选项</h3>
              {filteredResults.map((result) => (
                <Card
                  key={result.id}
                  className="cursor-pointer hover:shadow-md transition-all duration-200 bg-white border border-gray-200"
                  onClick={() => handleResultClick(result)}
                >
                  <CardContent className="p-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{result.flag}</span>
                      <div>
                        <div className="font-medium">{result.name}</div>
                        <div className="text-sm text-gray-500">
                          起价 {result.currency} {result.startingPrice.toFixed(2)}
                        </div>
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {activeTab === 'region' && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-600">区域</h3>
              {filteredResults.map((result) => (
                <Card
                  key={result.id}
                  className="cursor-pointer hover:shadow-md transition-all duration-200 bg-white border border-gray-200"
                  onClick={() => handleResultClick(result)}
                >
                  <CardContent className="p-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{result.flag}</span>
                      <div>
                        <div className="font-medium">{result.name}</div>
                        <div className="text-sm text-gray-500">
                          起价 {result.currency} {result.startingPrice.toFixed(2)} • {result.description}
                        </div>
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredResults.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">没有找到相关结果</div>
              <div className="text-sm text-gray-500">请尝试其他搜索词</div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
