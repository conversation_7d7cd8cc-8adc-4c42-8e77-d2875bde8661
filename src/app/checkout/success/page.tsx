"use client";

import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/main-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  Mail, 
  QrCode, 
  Download,
  Home,
  Smartphone,
  Clock,
  AlertCircle
} from "lucide-react";

// Mock order data
const mockOrder = {
  id: "ORD-2024-001",
  planName: "USA Unlimited",
  region: "North America",
  amount: 32.99,
  currency: "USD",
  email: "<EMAIL>",
  purchaseDate: new Date().toISOString(),
  qrCode: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSJ3aGl0ZSIvPgo8IS0tIFNpbXBsaWZpZWQgUVIgY29kZSBwYXR0ZXJuIC0tPgo8cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iMTIwIiB5PSIyMCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSIyMCIgeT0iMTIwIiB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjkwIiB5PSI5MCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSJibGFjayIvPgo8L3N2Zz4K",
};

const activationSteps = [
  {
    step: 1,
    title: "Check your email",
    description: "We've sent activation instructions to your email address",
    icon: Mail,
  },
  {
    step: 2,
    title: "Scan QR code",
    description: "Use your device's camera to scan the QR code in the email",
    icon: QrCode,
  },
  {
    step: 3,
    title: "Follow setup",
    description: "Complete the eSIM setup process on your device",
    icon: Smartphone,
  },
];

export default function CheckoutSuccessPage() {
  const router = useRouter();

  const handleDownloadQR = () => {
    // In a real app, this would download the actual QR code
    console.log("Download QR code");
  };

  const handleViewESIMs = () => {
    router.push("/esims");
  };

  const handleGoHome = () => {
    router.push("/");
  };

  return (
    <MainLayout showBottomNav={false}>
      <div className="space-y-6 p-4">
        {/* Success Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Payment Successful!</h1>
            <p className="text-muted-foreground">
              Your eSIM plan has been purchased successfully
            </p>
          </div>
        </div>

        {/* Order Details */}
        <Card>
          <CardHeader>
            <CardTitle>Order Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Order ID</span>
              <span className="font-mono">{mockOrder.id}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-muted-foreground">Plan</span>
              <span className="font-medium">{mockOrder.planName}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-muted-foreground">Region</span>
              <span>{mockOrder.region}</span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-muted-foreground">Amount Paid</span>
              <span className="font-semibold">
                ${mockOrder.amount} {mockOrder.currency}
              </span>
            </div>
            <Separator />
            <div className="flex justify-between">
              <span className="text-muted-foreground">Purchase Date</span>
              <span>
                {new Date(mockOrder.purchaseDate).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* QR Code */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              Activation QR Code
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="mx-auto w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center">
              <img 
                src={mockOrder.qrCode} 
                alt="eSIM Activation QR Code"
                className="w-40 h-40"
              />
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Scan this QR code with your device to activate your eSIM
              </p>
              <Button variant="outline" onClick={handleDownloadQR}>
                <Download className="h-4 w-4 mr-2" />
                Download QR Code
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Activation Steps */}
        <Card>
          <CardHeader>
            <CardTitle>How to Activate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activationSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={step.step} className="flex gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                      {step.step}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{step.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {step.description}
                      </p>
                    </div>
                    <Icon className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-1" />
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Important Notice */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
              <div className="space-y-1">
                <h4 className="font-medium">Important</h4>
                <p className="text-sm text-muted-foreground">
                  Your eSIM validity period starts when you activate it, not when you purchase it. 
                  You can activate it anytime within 30 days of purchase.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Confirmation */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-blue-500" />
              <div>
                <h4 className="font-medium">Email Sent</h4>
                <p className="text-sm text-muted-foreground">
                  Activation instructions sent to {mockOrder.email}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button className="w-full" onClick={handleViewESIMs}>
            <Smartphone className="h-4 w-4 mr-2" />
            View My eSIMs
          </Button>
          <Button variant="outline" className="w-full" onClick={handleGoHome}>
            <Home className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>

        {/* Support */}
        <Card>
          <CardContent className="p-4 text-center">
            <h4 className="font-medium mb-2">Need Help?</h4>
            <p className="text-sm text-muted-foreground mb-3">
              If you have any issues with activation, our support team is here to help.
            </p>
            <Button variant="outline" size="sm" onClick={() => router.push('/contact')}>
              Contact Support
            </Button>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
