// User types
export interface User {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  avatar?: string;
  balance: number;
  referralCode: string;
  createdAt: string;
  updatedAt: string;
}

// eSIM Plan types
export interface ESIMPlan {
  id: string;
  name: string;
  description: string;
  region: string;
  countries: string[];
  dataAmount: number; // in GB
  validityDays: number;
  price: number;
  currency: string;
  isHot?: boolean;
  isGlobal?: boolean;
  networkTypes: string[]; // ['4G', '5G']
  carriers: string[];
  coverageAreas: string[];
}

// Active eSIM types
export interface ActiveESIM {
  id: string;
  planId: string;
  plan: ESIMPlan;
  userId: string;
  orderId: string;
  status: 'active' | 'expired' | 'pending';
  dataUsed: number; // in GB
  dataRemaining: number; // in GB
  activatedAt?: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

// Order types
export interface Order {
  id: string;
  userId: string;
  planId: string;
  plan: ESIMPlan;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
  activatedAt?: string;
}

// Transaction types
export interface Transaction {
  id: string;
  userId: string;
  type: 'purchase' | 'topup' | 'refund' | 'referral_bonus';
  amount: number;
  currency: string;
  description: string;
  orderId?: string;
  createdAt: string;
}

// Search types
export interface SearchResult {
  type: 'country' | 'region' | 'plan';
  id: string;
  name: string;
  description?: string;
  flag?: string;
  plans?: ESIMPlan[];
}

// Carousel types
export interface CarouselItem {
  id: string;
  title: string;
  description: string;
  image: string;
  link: string;
  isActive: boolean;
}

// Region types
export interface Region {
  id: string;
  name: string;
  description: string;
  flag: string;
  planCount: number;
  startingPrice: number;
  currency: string;
  isPopular?: boolean;
}

// FAQ types
export interface FAQ {
  id: string;
  category: string;
  question: string;
  answer: string;
  order: number;
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdAt: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form types
export interface LoginFormData {
  email: string;
}

export interface ProfileFormData {
  name: string;
  phone?: string;
}

export interface SearchFormData {
  query: string;
  type?: 'all' | 'country' | 'region';
}
